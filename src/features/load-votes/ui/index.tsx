'use client';

import React, {useEffect, useState} from 'react';
import {getCaptcha, getInitiativeToken, getVotes, ExternalVoteCaptcha} from "@/shared/api/external";
import {toast} from "react-toastify";
import {loadVotes} from "@/entity/votes/api";

const base64 = (b: string) => {
    return `data:image/png;base64, ${b}`
}

type Props = {
    captcha: ExternalVoteCaptcha
}

export const LoadVotesForm = ({captcha: c}: Props) => {
    const [captcha, setCaptcha] = useState(() => c)
    const [captchaAns, setCaptchaAns] = useState('')

    const refreshCaptcha = async () => {
        const c = await getCaptcha()
        setCaptcha(c)
        setCaptchaAns('')
    }

    console.log(captcha)

    const load = async () => {
        let loadedCount = 0;
        try {

            const body = {
                initiativeId: "192db02b-019f-4645-8490-69f6e72902ad",
                captchaKey: captcha.captchaKey,
                captchaResult: captchaAns
            }

            const token = await getInitiativeToken(body)

            const r = async (page: number) => {
                const ph = await getVotes({page: page.toString(), token: token.token})

                if(ph.content?.length){
                    const res = await loadVotes(ph.content)

                    if(!res){
                        toast.error('Databasega yozishda xatolik')
                        return
                    }

                    loadedCount += ph.content.length

                    console.log('loaded page: ', page)
                    toast.success(`${page + 1} - sahifa muvaffaqqiyatli yuklandi!`)

                    if(res){
                        await r(page + 1)
                    }
                }
            }

            await r(0)

            toast.success('Ovozlar muvaffaqqiyatli yangilandi!', {
                toastId: 'load-json-success'
            })

        }catch (e) {
            toast.error(e as string, {
                toastId: 'load-json-error'
            })
        }

        if(loadedCount > 0){
            toast.info(`Yuklangan ovozlar soni: ${loadedCount}`)
        }
    }

    return (
        <div className='container mx-auto my-2 p-5'>
            <h1 className='text-center text-[25px]'>{"Ko'ktol uchun berilgan ovozlarni tekshirish"}</h1>
            <div className='flex center my-5 gap-x-5'>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img src={base64(captcha.image)} alt=""/>
                <button
                    onClick={refreshCaptcha}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-xl shadow-md border border-gray-300 hover:bg-gray-200 active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 transition"
                >
                    🔄 <span>Captchani yangilash</span>
                </button>
            </div>
            <div className='mb-5'>
                <input
                    className='w-full px-4 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-400'
                    placeholder='Captcha javobini kiriting'
                    value={captchaAns}
                    onChange={(e) => setCaptchaAns(e.target.value)}
                />
            </div>
            <>
                <div>
                    <button
                        className="cursor-pointer w-full px-6 py-2 bg-blue-500 text-white font-semibold rounded-xl shadow-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 transition disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                        disabled={!captchaAns}
                        onClick={load}
                    >
                        Ovozlarni yangilash
                    </button>
                </div>
            </>
        </div>
    );
};