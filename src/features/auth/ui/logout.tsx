"use client";

import React from 'react';
import {LogOutIcon} from "lucide-react";
import {Button} from "@/shared/ui/button";

export const Logout = () => {

  const handleLogout = () => {
    window.location.href = "/login";
  }

  return (
      <Button variant="ghost" size="icon" className="cursor-pointer" onClick={handleLogout}>
        <LogOutIcon width="16" height="16" />
      </Button>
  );
};
