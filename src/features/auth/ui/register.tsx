"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/shared/ui/card"
import { Input } from "@/shared/ui/input"
import { Label } from "@/shared/ui/label"
import {toast} from "sonner";

export function Register() {
    const [name, setName] = useState("")
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [confirmPassword, setConfirmPassword] = useState("")
    const [isLoading, setIsLoading] = useState(false)

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (password !== confirmPassword) {
            toast("Xatolik", {
                description: "Parollar mos kelmadi",
            })
            return
        }

        setIsLoading(true)

        try {
            const response = await fetch("/api/register", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ name, email, password, url: window.location.origin }),
            })

            if (response.ok) {
                toast("Muvaffaqiyatli", {
                    description: "Iltimos, elektron pochta manzilingizga yuborilgan havolani bosib hisobingizni tasdiqlang.",
                })
                setTimeout(() => {
                    window.location.href = "/login?registered=true"
                }, 2000)
            } else {
                toast("Xatolik", {
                    description: "Hisob yaratishda xatolik yuz berdi",
                })
            }
        } catch {
            toast("Xatolik", {
                description: "Hisob yaratishda xatolik yuz berdi",
            })
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="flex min-h-[80vh] items-center justify-center px-4">
            <Card className="w-full max-w-md">
                <CardHeader>
                    <CardTitle className="text-2xl">Hisob yaratish</CardTitle>
                    <CardDescription>Hisobingizni yaratish uchun elektron pochta va parolingizni kiriting</CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="email">Isminggiz</Label>
                            <Input
                              type="text"
                              placeholder="Isminggiz"
                              value={name}
                              onChange={(e) => setName(e.target.value)}
                              required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input
                              type="email"
                              placeholder="Email"
                              value={email}
                              onChange={(e) => setEmail(e.target.value)}
                              required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="password">Parol (6 ta belgidan kam bo'lmasin)</Label>
                            <Input
                              type="password"
                              placeholder="Parol"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              required
                              autoComplete="new-password"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="confirm-password">Parolni tasdiqlang</Label>
                            <Input
                              type="password"
                              placeholder="Parolni tasdiqlang"
                              value={confirmPassword}
                              onChange={(e) => setConfirmPassword(e.target.value)}
                              required
                            />
                        </div>
                        <Button type="submit" className="w-full" disabled={isLoading}>
                            {isLoading ? "Hisob yaratilmoqda..." : "Hisob yaratish"}
                        </Button>
                    </form>
                </CardContent>
                <CardFooter className="flex justify-center">
                    <p className="text-sm text-muted-foreground">
                        Hisobingiz bormi?{" "}
                        <Link href="/login" className="text-primary hover:underline">
                            Kirish
                        </Link>
                    </p>
                </CardFooter>
            </Card>
        </div>
    )
}

