import { useState } from "react";

export type ModalControlType<T = unknown> = {
    visible: boolean;
    modalProps: T;
    openModal: (props: T) => void;
    closeModal: () => void;
    updateProps: (props: T) => void;
    resetModal: () => void;
};

export const useModalControl = <T extends Record<string, unknown>>(initialModalProps?: T): ModalControlType<T> => {
    const [modalProps, setModalProps] = useState({ visible: false, modalProps: {...initialModalProps} });

    const openModal = (props: T) => {
        setModalProps({ visible: true, modalProps: { ...initialModalProps, ...props } });
    };

    const closeModal = () => {
        setModalProps({ ...modalProps, visible: false, modalProps: { ...initialModalProps } });
    };

    const updateProps = (props: T) => {
        setModalProps({ ...modalProps, ...props });
    };

    const resetModal = () => {
        setModalProps({ visible: false, modalProps: { ...initialModalProps } });
    };

    return {
        ...modalProps,
        openModal,
        closeModal,
        updateProps,
        resetModal,
    } as ModalControlType<T>;
};
