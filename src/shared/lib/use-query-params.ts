'use client';

import { ReadonlyURLSearchParams, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

const parseQueryParams = (searchParams: ReadonlyURLSearchParams) => {
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
        if(value !== 'undefined'){
            params[key] = value;
        }
    });
    return params;
};

/**
 * Кастомный хук для работы с query-параметрами в URL.
 *
 * @template QueryParams - Тип объекта query-параметров.
 *
 * @returns {Object} Хук для работы с query-параметрами.
 * @returns {QueryParams} queryParams - Объект текущих query-параметров.
 * @returns {(paramsToUpdate: Record<string, string | number | null>) => void} setQueryParams - Функция для обновления query-параметров.
 *
 * @example
 * const { queryParams, setQueryParams } = useQueryParams<{ page?: string }>();
 * console.log(queryParams.page); // Текущая страница из URL
 * setQueryParams({ page: '2' }); // Обновление параметра страницы
 *
 * <AUTHOR> Shodiyorov
 */

export const useQueryParams = <QueryParams extends Record<string, string | number | null | undefined | boolean>>() => {
    const router = useRouter();
    const searchParams = useSearchParams();

    // Локальный стейт для хранения query в виде объекта
    const [query, setQuery] = useState<QueryParams>(() => parseQueryParams(searchParams) as QueryParams);

    // Обновляем state при изменении URL
    useEffect(() => {
        setQuery(parseQueryParams(searchParams) as QueryParams);
    }, [searchParams]);

    // Функция для установки нескольких параметров сразу
    const setQueryParams = useCallback(
        (paramsToUpdate: Record<string, string | number | null>) => {
            const params = new URLSearchParams(searchParams.toString());

            Object.entries(paramsToUpdate).forEach(([key, value]) => {
                if (value === null) {
                    params.delete(key); // Удаляем параметр, если передан null
                } else {
                    params.set(key, String(value)); // Устанавливаем новое значение
                }
            });

            router.push(`?${params.toString()}`, { scroll: false });
        },
        [router, searchParams],
    );

    return { queryParams: query, setQueryParams };
};
