import nodemailer from 'nodemailer'

const userMail = process.env.USER_MAIL as string
const passwordMail = process.env.PASSWORD_MAIL as string

export async function mailer(
    to: string,
    subject: string,
    text: string,
    html = ''
): Promise<void> {
    const transporter = nodemailer.createTransport(
        {
            host: 'smtp.mail.ru',
            port: 465,
            secure: true,
            auth: {
                user: userMail,
                pass: passwordMail,
            },
            tls: {
                rejectUnauthorized: false,
            },
            debug: true,
        },
        {
            from: userMail,
        }
    )

    try {
        await transporter.verify()
        console.log('Server is ready to take our messages')
    } catch (error) {
        console.error('Error verifying transporter:', error)
    }

    try {
        const info = await transporter.sendMail({
            to,
            subject,
            text,
            html,
        })
        console.log('Email sent:', info)
    } catch (error) {
        console.error('Error sending email:', error)
    }
}
