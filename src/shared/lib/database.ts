import mongoose from "mongoose";

const MONGODB_URI = process.env.MONGODB_URI!;

if (!MONGODB_URI) {
    throw new Error("Добавь MONGODB_URI в .env.local");
}
// eslint-disable-next-line
let cached = (global as any).mongoose;

if (!cached) {
    // eslint-disable-next-line
    cached = (global as any).mongoose = { conn: null, promise: null };
}

export async function dbConnect() {
    try {

        if (cached.conn) return cached.conn;

        if (!cached.promise) {
            cached.promise = mongoose.connect(MONGODB_URI, {
                bufferCommands: false
            }).then((mongoose) => {
                console.log('Mongoose connected')
                return mongoose;
            })
        }

        cached.conn = await cached.promise;
        return cached.conn;

    }catch (err){
        cached.promise = null;
        throw err
    }

    return cached.conn;

}
