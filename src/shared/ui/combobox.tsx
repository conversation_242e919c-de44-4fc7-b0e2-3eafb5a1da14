"use client"

import * as React from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"
import {Popover, PopoverContent, PopoverTrigger} from "@/shared/ui/popover";
import {Button} from "@/shared/ui/button";
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/shared/ui/command";
import {cn} from "@/shared/lib";
import {ReactNode} from "react";

export type ComboboxItem = {
    value: string;
    label: ReactNode;
}

export type ComboboxProps = {
    items: ComboboxItem[];
    value?: string;
    onValueChange?: (value: string) => void;
    placeholder?: string;
    searchPlaceholder?: string;
    emptyMessage?: string;
    className?: string;
    width?: string;
    disabled?: boolean;
    clearable?: boolean; // Возможность очистить выбранное значение
    buttonProps?: React.ComponentProps<typeof Button>;
    popoverProps?: React.ComponentProps<typeof Popover>;
    popoverContentProps?: React.ComponentProps<typeof PopoverContent>;
    commandInputProps?: React.ComponentProps<typeof CommandInput>;
}

export function Combobox({
    items,
    value: controlledValue,
    onValueChange,
    placeholder = "Select an item...",
    searchPlaceholder = "Search...",
    emptyMessage = "No item found.",
    className,
    width = "w-[200px]",
    disabled = false,
    clearable = true, // По умолчанию можно очистить значение
    buttonProps,
    popoverProps,
    popoverContentProps,
    commandInputProps,
}: ComboboxProps) {
    const [open, setOpen] = React.useState(false)
    const [internalValue, setInternalValue] = React.useState(controlledValue || "")

    // Используем контролируемое значение, если оно предоставлено
    const value = controlledValue !== undefined ? controlledValue : internalValue

    // Обработчик изменения значения
    const handleValueChange = (newValue: string) => {
        if (controlledValue === undefined) {
            setInternalValue(newValue)
        }
        onValueChange?.(newValue)
    }


    return (
        <Popover
            open={open}
            modal={popoverProps?.modal}
            {...popoverProps}
            onOpenChange={(newOpen) => {
                // Проверяем, что не происходит очистка значения
                // Если пользователь кликнул на кнопку X, не открываем выпадающий список
                if (document.activeElement?.getAttribute('aria-label') === 'Clear selection') {
                    return;
                }

                setOpen(newOpen)
                popoverProps?.onOpenChange?.(newOpen)
            }}
        >
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(width, "justify-between flex items-center", className)}
                    disabled={disabled}
                    {...buttonProps}
                >
                    <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                        {value
                            ? items.find((item) => item.value === value)?.label
                            : placeholder}
                    </div>
                    <div className="flex items-center gap-2">
                        {clearable && value && (
                            <span
                                role="button"
                                className="h-5 w-5 rounded-full flex items-center justify-center bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 z-10"
                                onClick={(e) => {

                                    e.stopPropagation();
                                    e.preventDefault();
                                    // Закрываем выпадающий список, если он открыт
                                    if (open) {
                                        setOpen(false);
                                    }
                                    // Очищаем значение
                                    handleValueChange("");
                                }}
                                aria-label="Clear selection"
                            >
                                <X className="h-3 w-3" />
                            </span>
                        )}
                        <ChevronsUpDown className="opacity-50" />
                    </div>
                </Button>
            </PopoverTrigger>
            <PopoverContent
                className={cn(width, "p-0", popoverContentProps?.className)}
                style={{
                    width: width.includes('w-[') ? width.match(/w-\[(.*?)\]/)?.[1] : 'auto',
                    zIndex: 9999 // Устанавливаем высокий z-index
                }}
                align="start"
                sideOffset={5}
                {...popoverContentProps}
            >
                <Command shouldFilter={commandInputProps?.value === undefined}>
                    <CommandInput
                        placeholder={searchPlaceholder}
                        className="h-9"
                        {...commandInputProps}
                    />
                    <CommandList>
                        {items.length === 0 ? (
                            <CommandEmpty>{emptyMessage}</CommandEmpty>
                        ) : (
                            <CommandGroup>
                                {items.map((item) => (
                                    <CommandItem
                                        key={item.value}
                                        value={item.value}
                                        onSelect={(currentValue) => {
                                            const newValue = currentValue === value ? "" : currentValue
                                            handleValueChange(newValue)
                                            setOpen(false)
                                        }}
                                    >
                                        {item.label}
                                        <Check
                                            className={cn(
                                                "ml-auto",
                                                value === item.value ? "opacity-100" : "opacity-0"
                                            )}
                                        />
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        )}
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}
