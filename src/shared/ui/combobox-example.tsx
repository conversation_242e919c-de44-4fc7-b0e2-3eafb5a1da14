"use client"

import React, { useState } from "react"
import { Combobox, ComboboxItem } from "./combobox"

// Пример данных
const frameworks: ComboboxItem[] = [
    { value: "next.js", label: "Next.js" },
    { value: "sveltekit", label: "SvelteKit" },
    { value: "nuxt.js", label: "Nuxt.js" },
    { value: "remix", label: "Remix" },
    { value: "astro", label: "Astro" },
]

// Пример использования в контролируемом режиме
export function ComboboxExample() {
    const [value, setValue] = useState("")
    
    return (
        <div className="flex flex-col gap-4 p-4">
            <h2 className="text-lg font-semibold">Пример использования Combobox</h2>
            
            {/* Базовое использование */}
            <div>
                <h3 className="text-md mb-2">Базовый Combobox:</h3>
                <Combobox 
                    items={frameworks} 
                />
            </div>
            
            {/* Контролируемый Combobox */}
            <div>
                <h3 className="text-md mb-2">Контролируемый Combobox:</h3>
                <Combobox 
                    items={frameworks}
                    value={value}
                    onValueChange={setValue}
                    placeholder="Выберите фреймворк..."
                    searchPlaceholder="Поиск фреймворка..."
                    emptyMessage="Фреймворк не найден."
                />
                <p className="mt-2">Выбранное значение: {value || "не выбрано"}</p>
            </div>
            
            {/* Кастомизированный Combobox */}
            <div>
                <h3 className="text-md mb-2">Кастомизированный Combobox:</h3>
                <Combobox 
                    items={frameworks}
                    width="w-[300px]"
                    className="bg-blue-50"
                    buttonProps={{
                        variant: "default"
                    }}
                />
            </div>
            
            {/* Отключенный Combobox */}
            <div>
                <h3 className="text-md mb-2">Отключенный Combobox:</h3>
                <Combobox 
                    items={frameworks}
                    disabled={true}
                />
            </div>
        </div>
    )
}
