'use server';

import {
    ExternalInitiativeTokenModel,
    ExternalInitiativeTokenParams, ExternalPaginationList,
    ExternalVoteCaptcha,
    ExternalVoteParams,
    ExternalVotesModel
} from "@/shared/api/external/types";
import {http} from "@/shared/lib/http";


export async function getCaptcha (): Promise<ExternalVoteCaptcha> {
    try {
        const res = await http.get('/api/v1/captcha')
        return res.data
    }catch (e) {
        console.log(e)
        throw e
    }
}

export async function getInitiativeToken (body: ExternalInitiativeTokenParams): Promise<ExternalInitiativeTokenModel> {
    console.log(body)
    try {
        const res = await http.get('/api/v1/initiative-token', {data: body})
        return res.data
    }catch (e) {
        console.log('initiative token error: ',e)
        throw e
    }

    // {
    //     "token": "67d598def7e8d3111a8db269",
    //     "date": "2025-03-15T21:12:30.152+05:00"
    // }
}

export async function getVotes ({token, ...query}: ExternalVoteParams): Promise<ExternalPaginationList<ExternalVotesModel>> {
    try {
        const res = await http.get(`/api/v1/votes`, {params: {token, ...query}})
        return res.data
    }catch (e) {
        console.log('get votes error: ',e)
        throw e
    }
}