export type ExternalVoteCaptcha = {
    image: string;
    captchaKey: string;
}

export type ExternalInitiativeTokenParams = {
	initiativeId: string;
	captchaKey: string;
	captchaResult: string;
}

export type ExternalInitiativeTokenModel = {
    token: string;
    date: string;
}

export type ExternalVoteParams = {
    token: string;
    page?: string;
}

export type ExternalVotesModel = {
    image: string;
    voteDate: string;
}

export type ExternalPaginationList<T> = {
    content: T[];
    pageable: {
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        };
        offset: number;
        pageNumber: number;
        pageSize: number;
        paged: boolean;
        unpaged: boolean;
    }
    totalElements: number;
    totalPage: number;
    last: boolean;
    number: number;
    first: boolean;
    numberOfElements: number;
    size: number;
    empty: boolean;
}