import {NextRequest, NextResponse} from 'next/server'
import {dbConnect} from "@/shared/lib/database";
import {Candidate, UserSchema} from "@/entity/users/schema";

export async function GET(req: NextRequest) {
    try {
        const { searchParams } = new URL(req.url)
        const email = searchParams.get('email')
        const key = searchParams.get('key')

        if (!email || !key) {
            return NextResponse.json(
                { error: 'Неверная ссылка подтверждения' },
                { status: 400 }
            )
        }

        await dbConnect()

        const candidate = await Candidate.findOne({ email, uid: key })

        if (!candidate) {
            return NextResponse.json(
                { error: 'Пользователь не найден или уже подтверждён' },
                { status: 404 }
            )
        }

        const existingUser = await UserSchema.findOne({ email })
        if (existingUser) {
            await Candidate.deleteOne({ email, uid: key }) // очистка на всякий случай
            return NextResponse.redirect(new URL('/login', req.url))
        }

        const { name, passwordHash } = candidate

        await UserSchema.create({
            name,
            email,
            password: passwordHash,
            role: "USER"
        })

        await Candidate.deleteOne({ email, uid: key })

        return NextResponse.redirect(new URL('/login', req.url))
    } catch (err) {
        console.error('Confirm error:', err)
        return NextResponse.json(
            { error: 'Ошибка при подтверждении регистрации' },
            { status: 500 }
        )
    }
}
