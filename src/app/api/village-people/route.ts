import { NextRequest, NextResponse } from 'next/server';
import { createVillagePeople } from '@/entity/village-people/api/create';

export const config = {
    runtime: 'nodejs',
};

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const { index, name, phone, mahalla, street } = body;

        // Валидация обязательных полей
        if (!index || !name || !phone || !mahalla || !street) {
            return NextResponse.json({
                success: false,
                error: 'Barcha maydonlar to\'ldirilishi shart'
            }, { status: 400 });
        }

        const result = await createVillagePeople({
            index,
            name,
            phone,
            mahalla,
            street,
        });

        if (result.success) {
            return NextResponse.json({
                success: true,
                data: result.data
            });
        } else {
            return NextResponse.json({
                success: false,
                error: result.error
            }, { status: 400 });
        }
    } catch (error) {
        console.error('Error creating village people:', error);
        return NextResponse.json({
            success: false,
            error: 'Xo\'jalik yaratishda xatolik yuz berdi'
        }, { status: 500 });
    }
}
