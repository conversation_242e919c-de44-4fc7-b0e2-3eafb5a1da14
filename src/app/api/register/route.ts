import {NextRequest, NextResponse} from "next/server"
import bcrypt from "bcryptjs"
import {z} from "zod";
import {nanoid} from "nanoid";
import {mailer} from "@/shared/lib/mailer";
import {dbConnect} from "@/shared/lib/database";
import {Candidate, UserSchema} from "@/entity/users/schema";
import {registerApiSchema} from "@/shared/schema/register";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password, url } = registerApiSchema.parse(body)

    await dbConnect()

    // Проверяем, существует ли пользователь
    const existingUser = await UserSchema.findOne({ email })
    if (existingUser) {
      return NextResponse.json(
        { error: "Пользователь с таким email уже существует" },
        { status: 400 }
      )
    }

    // Хешируем пароль
    const passwordHash = await bcrypt.hash(password, 12)
    const uid = nanoid(10)

    // Создаем пользователя
    const candidate = await Candidate.create({
      uid,
      name,
      email,
      passwordHash,
    })

    await candidate.save()

    const html = `
      <div>
        <h2 style="text-align: center; padding: 0px 0px 10px;">
          Roʻyxatdan oʻtishni tasdiqlash uchun havolaga oʻting
        </h2>
        <div>
          <h1 style="text-align: center; padding: 10px 0px;">
            <a 
              style="background: #3c2eff; color: #fff; padding: 5px 20px 6px; border-radius: 5px;" 
              href="${url}/api/auth/confirm?key=${uid}&email=${email}"
            >
              Tasdiqlang
            </a>
          </h1>
        </div>
      </div>
    `

    await mailer(
      email,
      "Регистрация на сайте",
      `Для подтверждения регистрации перейдите по ссылке`,
        html
    )

    return NextResponse.json(
      { message: "Пользователь успешно зарегистрирован, проверьте почту" },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error("Registration error:", error)
    return NextResponse.json(
      { error: "Внутренняя ошибка сервера" },
      { status: 500 }
    )
  }
}
