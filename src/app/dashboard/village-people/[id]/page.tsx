import { getVillagePeopleById } from "@/entity/village-people/api";
import { VillagePeopleDetail } from "@/entity/village-people/ui";
import { notFound } from "next/navigation";

type Props = {
    params: Promise<{
        id: string;
    }>;
};

export default async function VillagePeopleDetailPage({ params }: Props) {
    const { id } = await params;
    const villagePerson = await getVillagePeopleById(id);

    if (!villagePerson) {
        notFound();
    }

    return (
        <>
            <VillagePeopleDetail villagePerson={villagePerson} />
        </>
    );
}

// Sahifa uchun meta ma'lumotlarni yaratish
export async function generateMetadata({ params }: Props) {
    const { id } = await params;
    const villagePerson = await getVillagePeopleById(id);

    if (!villagePerson) {
        return {
            title: "Xo'jalik topilmadi",
        };
    }

    return {
        title: `${villagePerson.name} | Batafsil ma'lumot`,
        description: `Xo'jalik haqida batafsil ma'lumot: ${villagePerson.name}`,
    };
}
