import Link from "next/link";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { ArrowLeft } from "lucide-react";

export default function VillagePeopleNotFound() {
    return (
        <div className="container mx-auto py-16 px-4 text-center">
            <div className="max-w-md mx-auto">
                <h1 className="text-4xl font-bold mb-4">404</h1>
                <h2 className="text-2xl font-semibold mb-6">{"Xo'jalik topilmadi"}</h2>
                <p className="text-gray-600 mb-8">
                    {"So'ralgan xo'jalik mavjud emas yoki o'chirilgan."}
                </p>
                <Link href="/dashboard/village-people">
                    <Button className="flex items-center gap-2 mx-auto">
                        <ArrowLeft className="h-4 w-4" />
                        {"Xo'jaliklar ro'yxatiga qaytish"}
                    </Button>
                </Link>
            </div>
        </div>
    );
}
