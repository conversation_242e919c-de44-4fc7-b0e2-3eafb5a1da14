"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { createVillagePeople, CreateVillagePeopleParams } from "../api/create"
import { toast } from "sonner"

/**
 * Хук для создания нового VillagePeople
 */
export function useCreateVillagePeople() {
  const queryClient = useQueryClient()
  
  const mutation = useMutation({
    mutationFn: createVillagePeople,
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Xo'jalik muvaffaqiyatli yaratildi")
        
        // Инвалидируем кеш для обновления списка
        queryClient.invalidateQueries({ queryKey: ["villagePeopleList"] })
        queryClient.invalidateQueries({ queryKey: ["villagePeopleSelect"] })
        
        return true
      } else {
        toast.error(data.error || "Xo'jalik yaratishda xatolik yuz berdi")
        return false
      }
    },
    onError: (error) => {
      console.error("Error creating village people:", error)
      toast.error("Xo'jalik yaratishda xatolik yuz berdi")
      return false
    }
  })
  
  return mutation
}
