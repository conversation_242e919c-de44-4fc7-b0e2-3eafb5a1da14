import { z } from "zod"

/**
 * Схема для формы создания VillagePeople
 */
export const createVillagePeopleSchema = z.object({
  index: z.string()
    .min(1, { message: "Indeks kiritilishi shart" })
    .max(50, { message: "Indeks 50 ta belgidan oshmasligi kerak" }),
  name: z.string()
    .min(1, { message: "Ism kiritilishi shart" })
    .max(100, { message: "Ism 100 ta belgidan oshmasligi kerak" }),
  phone: z.string()
    .min(1, { message: "Telefon raqami kiritilishi shart" })
    .regex(/^\+?[0-9]{7,15}$/, {
      message: "Telefon raqami noto'g'ri formatda. Misol: +998901234567"
    }),
  mahalla: z.string()
    .min(1, { message: "Mahalla nomi kiritilishi shart" })
    .max(100, { message: "Mahalla nomi 100 ta belgidan oshmasligi kerak" }),
  street: z.string()
    .min(1, { message: "Ko'cha nomi kiritilishi shart" })
    .max(100, { message: "Ko'cha nomi 100 ta belgidan oshmasligi kerak" }),
})

export type CreateVillagePeopleFormValues = z.infer<typeof createVillagePeopleSchema>
