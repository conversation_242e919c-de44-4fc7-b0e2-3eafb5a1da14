import {createPaginationList, PaginationList} from "@/shared/api/external";
import {IVillagePeopleListModel} from "@/entity/village-people/api/list";
import {makeAutoObservable} from "mobx";

export class VillagePeopleListModel {
    villagePeople: PaginationList<IVillagePeopleListModel>

    constructor() {
        this.villagePeople = createPaginationList<IVillagePeopleListModel>()
        makeAutoObservable(this, {}, {autoBind: true})
    }

    setVillagePeople(villagePeople: PaginationList<IVillagePeopleListModel>) {
        this.villagePeople = villagePeople
    }
}