"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { useDebounce } from "@/shared/lib/use-debounce"
import { IVillagePeopleListModel, TVillagePeopleListParams, getVillagePeopleListPaginated } from "../api/list"
import { PaginationList } from "@/shared/api/external"
import {toast} from "sonner";

/**
 * Хук для получения списка хозяйств с возможностью поиска и пагинации
 */
export function useVillagePeopleList(initialParams: TVillagePeopleListParams = {}) {
  const [params, setParams] = useState<TVillagePeopleListParams>({
    page: 1,
    size: 20,
    search: "",
    ...initialParams
  })

  // Используем debounce для поискового запроса
  const debouncedSearch = useDebounce(params.search || "", 300)

  // Создаем параметры запроса с учетом debounce
  const queryParams = {
    ...params,
    search: debouncedSearch
  }

  // Запрос данных с использованием react-query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<PaginationList<IVillagePeopleListModel>>({
    queryKey: ["villagePeopleList", queryParams],
    queryFn: async () => {
      try {
        return await getVillagePeopleListPaginated(queryParams)
      } catch (error) {
        console.error("Error fetching village people list:", error)
        toast("Xatolik",{
          description: "Xo'jaliklarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    }
  })

  // Обработчик изменения параметров поиска
  const handleSearchChange = (search: string) => {
    setParams(prev => ({
      ...prev,
      search,
      page: 1 // Сбрасываем страницу при изменении поиска
    }))
  }

  // Обработчик изменения страницы
  const handlePageChange = (page: number) => {
    setParams(prev => ({
      ...prev,
      page
    }))
  }

  // Обработчик изменения размера страницы
  const handlePageSizeChange = (size: string) => {
    setParams(prev => ({
      ...prev,
      size: Number(size),
      page: 1 // Сбрасываем страницу при изменении размера
    }))
  }

  return {
    villagePeople: data?.data || [],
    total: data?.total || 0,
    totalPages: data?.totalPages || 0,
    currentPage: data?.currentPage || 1,
    search: params.search || "",
    pageSize: params.size || 20,
    isLoading,
    isError,
    error,
    refetch,
    handleSearchChange,
    handlePageChange,
    handlePageSizeChange
  }
}
