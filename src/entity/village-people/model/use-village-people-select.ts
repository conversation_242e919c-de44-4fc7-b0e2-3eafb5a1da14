"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { ComboboxItem } from "@/shared/ui/combobox"
import { useDebounce } from "@/shared/lib/use-debounce"
import qs from "query-string";

/**
 * Хук для получения данных для селекта villagePeople
 */
export function useVillagePeopleSelect(initialSearch = "") {
  const [search, setSearch] = useState(initialSearch)
  const debouncedSearch = useDebounce(search, 300)

  // Запрос данных с использованием TanStack Query
  const {
    data: items = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["villagePeopleSelect", debouncedSearch],
    queryFn: async () => {
      try {

        const query = {
          search: debouncedSearch,
          limit: 1000,
        }

        const response = await fetch(
          `/api/village-people/select?${qs.stringify(query)}`
        )

        if (!response.ok) {
          throw new Error("Failed to fetch village people")
        }

        const data = await response.json()

        if (data.success) {
          return data.data as ComboboxItem[]
        } else {
          throw new Error(data.error || "Failed to fetch village people")
        }
      } catch (error) {
        console.error("Error fetching village people:", error)
        throw error
      }
    },
    // Не запрашивать данные автоматически при монтировании, если поиск пустой
    enabled: true,
    // Кэшировать данные на 5 минут
    staleTime: 5 * 60 * 1000,
  })

  return {
    items,
    isLoading,
    isError,
    error,
    search,
    setSearch,
    refetch,
  }
}
