'use server';

import { dbConnect } from "@/shared/lib/database";
import { VillagePeopleSchema } from "@/entity/village-people/schema";
import { ComboboxItem } from "@/shared/ui/combobox";

export type VillagePeopleSelectParams = {
    search?: string;
    limit?: number;
}

/**
 * Получает список villagePeople для использования в селекте
 */
export async function getVillagePeopleForSelect({
    search = '',
    limit = 20
}: VillagePeopleSelectParams = {}): Promise<ComboboxItem[]> {
    await dbConnect();

    try {
        const searchRegex = new RegExp(search, 'i');
        const filter = search
            ? {
                $or: [
                    { name: searchRegex },
                    { phone: searchRegex },
                    { mahalla: searchRegex },
                    { street: searchRegex },
                ],
            }
            : {};

        console.log('Search query:', search, 'Filter:', filter);

        const villagePeople = await VillagePeopleSchema.find(filter)
            .limit(limit)
            .sort({ name: 1 })
            .select('_id name')
            .lean();

        console.log('Found village people:', villagePeople.length);

        const result = villagePeople.map(person => ({
            value: person._id.toString(),
            label: person.name
        }));

        return result;
    } catch (error) {
        console.error('Error in getVillagePeopleForSelect:', error);
        return [];
    }
}
