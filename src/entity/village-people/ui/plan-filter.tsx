"use client"

import React from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select"
import { Label } from "@/shared/ui/label"

type PlanFilterProps = {
    value?: string
    onValueChange?: (value: string) => void
    className?: string
}

export function PlanFilter({ value, onValueChange, className }: PlanFilterProps) {
    return (
        <div className={`space-y-2 ${className}`}>
            <Label htmlFor="plan-filter">Plan bo'yicha filter</Label>
            <Select value={value} onValueChange={onValueChange}>
                <SelectTrigger id="plan-filter" className="w-[200px]">
                    <SelectValue placeholder="Barchasi" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="none">Barchasi</SelectItem>
                    <SelectItem value="completed">Plan bajarganlar (≥50 ta ovoz)</SelectItem>
                    <SelectItem value="not_completed">Plan bajarmaganlar (&lt;50 ta ovoz)</SelectItem>
                </SelectContent>
            </Select>
        </div>
    )
}
