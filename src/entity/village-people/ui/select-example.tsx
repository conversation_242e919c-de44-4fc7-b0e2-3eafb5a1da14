"use client"

import React, { useState } from "react"
import { VillagePeopleSelect } from "./select"
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card"

export function VillagePeopleSelectExample() {
    const [selectedVillagePerson, setSelectedVillagePerson] = useState("")

    return (
        <Card className="w-full max-w-md mx-auto">
            <CardHeader>
                <CardTitle>Выбор хозяйства</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <label className="text-sm font-medium mb-2 block">
                        Выберите хозяйство
                    </label>
                    <VillagePeopleSelect
                        value={selectedVillagePerson}
                        onValueChange={setSelectedVillagePerson}
                        width="w-full"
                        clearable={true} // Можно очистить выбранное значение
                    />
                </div>

                {selectedVillagePerson && (
                    <div className="p-4 bg-gray-100 rounded-md">
                        <p className="text-sm">
                            <strong>Выбранное хозяйство ID:</strong> {selectedVillagePerson}
                        </p>
                    </div>
                )}

                <div className="mt-8">
                    <label className="text-sm font-medium mb-2 block">
                        Выберите хозяйство (без возможности очистки)
                    </label>
                    <VillagePeopleSelect
                        value={selectedVillagePerson}
                        onValueChange={setSelectedVillagePerson}
                        width="w-full"
                        clearable={false} // Нельзя очистить выбранное значение
                    />
                </div>
            </CardContent>
        </Card>
    )
}
