"use client"

import React, { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/ui/table"
import { SimplePagination } from "@/shared/ui/pagination"
import { Card, CardContent } from "@/shared/ui/card"
import { PaginationList } from "@/shared/api/external";
import { useQueryParams } from "@/shared/lib/use-query-params";
import { IVillagePeopleListModel, TVillagePeopleListParams } from "@/entity/village-people/api/list";
import Link from "next/link";
import { Button } from "@/shared/ui/button";
import { Eye, Plus } from "lucide-react";
import { CreateVillagePeopleModal } from "./create-modal";
import { PlanFilter } from "./plan-filter";


type Props = {
    villagePeople: PaginationList<IVillagePeopleListModel>
}

export const VillagePeopleList = ({villagePeople}: Props) => {
    const {queryParams, setQueryParams} = useQueryParams<TVillagePeopleListParams>()
    const currentPage = Number(queryParams.page || '1')
    const rowsPerPage = Number(queryParams.size || '20')
    const [createModalOpen, setCreateModalOpen] = useState(false)

    const currentItems = villagePeople.data

    // Handle page change
    const handlePageChange = (page: number) => {
        setQueryParams({
            page: page.toString()
        })
    }

    const handleRowsPerPageChange = (value: string) => {
        setQueryParams({
            page: '1',
            size: value
        })
    }

    // Handle plan filter change
    const handlePlanFilterChange = (value: string) => {
        setQueryParams({
            page: '1',
            // @ts-ignore
            planFilter: value === "none" ? undefined : (value || undefined)
        })
    }

    const handleCreateSuccess = () => {
        // Обновление произойдет автоматически через invalidateQueries в хуке
        console.log("Village people created successfully")
    }

    console.log(villagePeople)

    return (
        <>
            <div className="flex justify-between items-center mb-8">
                <h1 className="text-3xl font-bold">{"Xo'jaliklar: "} {villagePeople.total}</h1>
                <Button
                    onClick={() => setCreateModalOpen(true)}
                    className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                >
                    <Plus className="h-4 w-4" />
                    {"Yangi xo'jalik qo'shish"}
                </Button>
            </div>

            <CreateVillagePeopleModal
                open={createModalOpen}
                onOpenChange={setCreateModalOpen}
                onSuccess={handleCreateSuccess}
            />

            {/* Фильтр по плану */}
            <div className="mb-6">
                <PlanFilter
                    value={queryParams.planFilter}
                    onValueChange={handlePlanFilterChange}
                />
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[80px]">T/R</TableHead>
                            <TableHead>F.I.SH</TableHead>
                            <TableHead>Telefon raqami</TableHead>
                            <TableHead>Mahalla</TableHead>
                            <TableHead className="w-[100px] text-center">Ovozlar soni</TableHead>
                            <TableHead className="w-[100px] text-right">Amallar</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <>
                            {currentItems.map((person, index) => (
                                <TableRow key={person._id}>
                                    <TableCell>{(currentPage - 1) * rowsPerPage + index + 1}</TableCell>
                                    <TableCell>{person.name}</TableCell>
                                    <TableCell>{person.phone}</TableCell>
                                    <TableCell>{person.mahalla}</TableCell>
                                    <TableCell className="text-center">
                                        <span className={`font-medium ${
                                            (person.votesCount || 0) >= 50
                                                ? 'text-green-600'
                                                : 'text-red-600'
                                        }`}>
                                            {person.votesCount || 0}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Link href={`/dashboard/village-people/${person._id}`}>
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 cursor-pointer">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </>
                    </TableBody>
                </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
                {currentItems.map((person) => (
                    <Card key={person._id} className="overflow-hidden py-1">
                        <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                                <div className="flex-1">
                                    <h3 className="font-medium">{person.name}</h3>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Telefon raqami: {person.phone}</p>
                                        <p>{person.mahalla}</p>
                                        <p className="mt-1">
                                            Ovozlar:
                                            <span className={`ml-1 font-medium ${
                                                (person.votesCount || 0) >= 50
                                                    ? 'text-green-600'
                                                    : 'text-red-600'
                                            }`}>
                                                {person.votesCount || 0}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <Link href={`/dashboard/village-people/${person._id}`}>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
            <SimplePagination
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                totalPages={villagePeople.totalPages}
                handlePageChange={handlePageChange}
                handleRowsPerPageChange={handleRowsPerPageChange}
                contentLength={villagePeople.data.length}
                total={villagePeople.total}
            />
        </>
    )
}

