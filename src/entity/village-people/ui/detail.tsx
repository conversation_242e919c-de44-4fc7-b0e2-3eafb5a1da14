"use client"

import React, { useState } from "react"
import { VillagePeopleDetailModel } from "../api"
import { Card, CardHeader, CardTitle } from "@/shared/ui/card"
import { Button } from "@/shared/ui/button"
import { ArrowLeft, Phone, Plus, Vote, Loader2 } from "lucide-react"
import Link from "next/link"
import { VoteSelectModal } from "./vote-select-modal"
import { useVillagePersonVotes } from "../model/use-village-person-votes"
import Image from "next/image";

type VillagePeopleDetailProps = {
    villagePerson: VillagePeopleDetailModel
}

export function VillagePeopleDetail({ villagePerson }: VillagePeopleDetailProps) {
    const [modalOpen, setModalOpen] = useState(false)

    // Используем кастомный хук для получения голосов
    const { votes, isLoading, refetch } = useVillagePersonVotes(villagePerson._id)

    // Обработчик успешного добавления голосов
    const handleVotesAdded = () => {
        refetch()
    }

    return (
        <div className="container mx-auto py-3 px-2 sm:px-4">
            <div className="flex justify-between items-center mb-3">
                <Link href="/dashboard/village-people">
                    <Button variant="outline" size="sm" className="flex items-center gap-1 cursor-pointer h-8">
                        <ArrowLeft className="h-3 w-3" />
                        {"Ro'yxatga qaytish"}
                    </Button>
                </Link>

                <Button
                    onClick={() => setModalOpen(true)}
                    className="bg-green-600 hover:bg-green-700 flex items-center gap-1 h-8"
                    size="sm"
                >
                    <Plus className="h-3 w-3" />
                    {"Ovoz qo'shish"}
                </Button>
            </div>

            {/* Модальное окно для выбора голосов */}
            <VoteSelectModal
                open={modalOpen}
                onOpenChange={setModalOpen}
                ownerId={villagePerson._id}
                onSuccess={handleVotesAdded}
            />

            <Card className="w-full mx-auto shadow-md">
                <CardHeader className="bg-gray-50 border-b py-3 px-4">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-lg font-bold">{villagePerson.name}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">{villagePerson.phone}</span>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            <div className="mt-4">
                <div className="flex justify-between items-center mb-3">
                    <h2 className="text-lg font-bold">{"Ovozlar ro'yxati"} ({votes.length})</h2>
                    <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 flex items-center gap-1"
                        onClick={() => setModalOpen(true)}
                    >
                        <Plus className="h-3 w-3" />
                        {"Ovoz qo'shish"}
                    </Button>
                </div>

                {isLoading ? (
                    <div className="bg-gray-100 rounded-lg p-6 text-center">
                        <Loader2 className="h-10 w-10 mx-auto text-gray-400 mb-3 animate-spin" />
                        <p className="text-gray-500 text-sm">{"Yuklanmoqda..."}</p>
                    </div>
                ) : votes.length === 0 ? (
                    <div className="bg-gray-100 rounded-lg p-6 text-center">
                        <Vote className="h-10 w-10 mx-auto text-gray-400 mb-3" />
                        <p className="text-gray-500 text-sm">{"Hozircha ovozlar yo'q"}</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                        {votes.map((vote) => (
                            <div key={vote._id} className="bg-white border rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
                                <div className="flex items-center gap-3">
                                    {vote.imageKey && (
                                        <div className="relative bg-gray-100">
                                            <Image
                                                src={`/votePictures/${vote.imageKey}.png`}
                                                alt={vote.imageKey}
                                                width={40}
                                                height={40}
                                            />
                                        </div>
                                    )}
                                    <div>
                                        {vote.phoneNumber && (
                                            <div className="flex items-center gap-1">
                                                <Phone className="h-3 w-3 text-gray-500" />
                                                <span className="text-sm font-medium">{vote.phoneNumber}</span>
                                            </div>
                                        )}
                                        {vote.voteDate && (
                                            <div className="text-xs text-gray-500">
                                                {vote.voteDate}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}
