"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/shared/ui/dialog"
import { Button } from "@/shared/ui/button"
import { Input } from "@/shared/ui/input"
import { Label } from "@/shared/ui/label"
import { Checkbox } from "@/shared/ui/checkbox"
import { Loader2, Phone, Search } from "lucide-react"
import Image from "next/image"
import { useVotesForSelect, useUpdateVotes } from "@/entity/votes/model/use-votes"
import {Controller, useForm} from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { voteSelectSchema, VoteSelectFormValues } from "@/entity/votes/model/schema"

type VoteSelectModalProps = {
    open: boolean
    onOpenChange: (open: boolean) => void
    ownerId: string
    onSuccess?: () => void
}

export function VoteSelectModal({ open, onOpenChange, ownerId, onSuccess }: VoteSelectModalProps) {
    // Используем кастомный хук для получения голосов
    const { votes, isLoading, search, setSearch } = useVotesForSelect()

    // Используем кастомный хук для обновления голосов
    const updateVotesMutation = useUpdateVotes()

    // Используем react-hook-form с zod для валидации
    const { control, handleSubmit, setValue, watch, reset, formState: { errors, isSubmitting } } = useForm<VoteSelectFormValues>({
        resolver: zodResolver(voteSelectSchema),
        defaultValues: {
            voteIds: []
        }
    })

    // Получаем текущие выбранные голоса
    const selectedVoteIds = watch('voteIds')

    // Сбрасываем форму при закрытии модального окна
    React.useEffect(() => {
        if (!open) {
            reset()
            setSearch('')
        }
    }, [open, reset, setSearch])

    // Обработка выбора голоса
    const handleVoteToggle = (voteId: string) => {
        const currentVoteIds = [...selectedVoteIds]
        const index = currentVoteIds.indexOf(voteId)

        if (index > -1) {
            currentVoteIds.splice(index, 1)
        } else {
            currentVoteIds.push(voteId)
        }

        setValue('voteIds', currentVoteIds, { shouldValidate: true })
    }

    // Обработка выбора всех голосов
    const handleSelectAll = () => {
        if (selectedVoteIds.length === votes.length) {
            setValue('voteIds', [], { shouldValidate: true })
        } else {
            setValue('voteIds', votes.map(vote => vote._id), { shouldValidate: true })
        }
    }

    // Сохранение выбранных голосов
    const onSubmit = async (data: VoteSelectFormValues) => {
        const result = await updateVotesMutation.mutateAsync({
            voteIds: data.voteIds,
            ownerId
        })

        if (result) {
            onOpenChange(false)
            onSuccess?.()
        }
    }

    // Получение последних 4 цифр номера телефона
    const getLastFourDigits = (phoneNumber?: string) => {
        if (!phoneNumber) return ""
        return phoneNumber.slice(-4)
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>Ovozlarni tanlash</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4 my-4">
                        <div className="flex items-center space-x-2">
                            <div className="relative flex-1">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Telefon raqami bo'yicha qidirish (to'liq yoki oxirgi 4 raqam)"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="pl-8"
                                />
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="select-all"
                                checked={selectedVoteIds.length > 0 && selectedVoteIds.length === votes.length}
                                onCheckedChange={handleSelectAll}
                            />
                            <Label htmlFor="select-all">Barchasini tanlash ({votes.length})</Label>
                        </div>

                        {errors.voteIds && (
                            <div className="text-sm text-red-500">
                                {errors.voteIds.message}
                            </div>
                        )}

                        {isLoading ? (
                            <div className="flex justify-center items-center py-8">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                        ) : votes.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                                {search ? "Qidiruv bo'yicha ovozlar topilmadi" : "Ovozlar mavjud emas"}
                            </div>
                        ) : (
                            <div className="space-y-2">
                                <Controller
                                    control={control}
                                    name="voteIds"
                                    render={({ field }) => (
                                        <>
                                            {votes.map((vote) => (
                                                <div
                                                    key={vote._id}
                                                    className="flex items-center space-x-3 border rounded-md p-3 hover:bg-accent"
                                                >
                                                    <Checkbox
                                                        id={vote._id}
                                                        checked={selectedVoteIds.includes(vote._id)}
                                                        onCheckedChange={() => handleVoteToggle(vote._id)}
                                                    />
                                                    <div className="flex items-center space-x-3 flex-1">
                                                        {vote.imageKey && (
                                                            <div className="h-10 w-10 relative rounded-full overflow-hidden bg-gray-100">
                                                                <Image
                                                                    src={`/votePictures/${vote.imageKey}.png`}
                                                                    alt="Vote"
                                                                    fill
                                                                    className="object-cover"
                                                                />
                                                            </div>
                                                        )}
                                                        <div className="flex-1">
                                                            <div className="flex items-center space-x-2">
                                                                <Phone className="h-3 w-3 text-muted-foreground" />
                                                                <span className="text-sm font-medium">
                                                                    {vote.phoneNumber}
                                                                </span>
                                                                {vote.phoneNumber && (
                                                                    <span className="text-xs text-muted-foreground">
                                                                        (oxirgi 4 raqam: {getLastFourDigits(vote.phoneNumber)})
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <div className="text-xs text-muted-foreground">
                                                                {vote.voteDate && `Sana: ${vote.voteDate}`}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </>
                                    )}
                                />
                            </div>
                        )}
                    </div>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            disabled={isSubmitting}
                        >
                            Bekor qilish
                        </Button>
                        <Button
                            type="submit"
                            disabled={selectedVoteIds.length === 0 || isSubmitting}
                        >
                            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            Saqlash ({selectedVoteIds.length})
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
