"use client"

import React from "react"
import { Combobox } from "@/shared/ui/combobox"
import { useVillagePeopleSelect } from "../model/use-village-people-select"

type VillagePeopleSelectProps = {
    value?: string
    onValueChange?: (value: string) => void
    placeholder?: string
    searchPlaceholder?: string
    emptyMessage?: string
    className?: string
    width?: string
    disabled?: boolean
    clearable?: boolean // Возможность очистить выбранное значение
}

export function VillagePeopleSelect({
    value,
    onValueChange,
    placeholder = "Выберите хозяйство...",
    searchPlaceholder = "Поиск хозяйства...",
    emptyMessage = "Хозяйство не найдено",
    className,
    width,
    disabled,
    clearable = true, // По умолчанию можно очистить значение
}: VillagePeopleSelectProps) {
    // Используем кастомный хук для получения данных
    const {
        items,
        isLoading,
        search,
        setSearch,
        refetch
    } = useVillagePeopleSelect()

    return (
        <Combobox
            items={items}
            value={value}
            onValueChange={onValueChange}
            placeholder={isLoading ? "Загрузка..." : placeholder}
            searchPlaceholder={searchPlaceholder}
            emptyMessage={isLoading ? "Загрузка..." : emptyMessage}
            className={className}
            width={width || "w-[300px]"}
            disabled={disabled || isLoading}
            clearable={clearable}
            commandInputProps={{
                value: search,
                onValueChange: setSearch,
            }}
            popoverProps={{
                onOpenChange: (open) => {
                    console.log('Popover onOpenChange:', open)
                    if (open) {
                        console.log('Fetching data on open')
                        void refetch()
                    }
                },
                // Убедиться, что popover отображается поверх всех элементов
                modal: true
            }}
        />
    )
}
