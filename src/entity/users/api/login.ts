'use server';

import {UserSchema} from "@/entity/users/schema";
import {comparePassword} from "@/entity/users/api/hash";
import {signJwt} from "@/entity/users/api/jwt";
import {cookies} from "next/headers";
import {User} from "@/entity/users/model";
import {dbConnect} from "@/shared/lib/database";

export type LoginModel = {
    success: boolean;
    error?: string;
    user?: User;
}

export async function login(formData: FormData): Promise<LoginModel> {
    const email = formData.get("email") as string
    const password = formData.get("password") as string

    if (!email || !password) {
        return {
            success: false,
            error: "Please enter both email and password",
        }
    }

    await dbConnect()
    const user = await UserSchema.findOne({email})

    if(!user){
        return {
            success: false,
            error: "Please enter both email and password",
        }
    }

    const compared = await comparePassword(password, user.password)

    if(!compared){
        return {
            success: false,
            error: "Please enter both email and password",
        }
    }

    const token = await signJwt({ userId: user._id.toString() });

    const cks = await cookies()

    cks.set("token", token, {
        httpOnly: true,
        path: "/",
        maxAge: 60 * 60 * 24 * 7, // 7 дней
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
    });

    return {
        success: true,
        user: {
            email,
            role: 'SUPER_ADMIN',
            id: user._id.toString()
        }
    }
}