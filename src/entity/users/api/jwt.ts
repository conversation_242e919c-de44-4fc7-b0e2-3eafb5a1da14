import {SignJWT, jwtVerify, JWTPayload} from 'jose';
import ms from 'ms';

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET!);

export async function signJwt(payload: object, expiresIn: ms.StringValue = '1w') {
    // Создание JWT с явным указанием заголовка
    const jwt = await new SignJWT(payload as JWTPayload)
        .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })  // Устанавливаем заголовок
        .setIssuedAt()
        .setExpirationTime(expiresIn) // Устанавливаем время истечения
        .sign(JWT_SECRET);

    return jwt;
}

export async function verifyJwt(token: string) {
    try {
        // Проверка JWT
        const { payload } = await jwtVerify(token, JWT_SECRET);
        return payload;
    } catch (err) {
        console.error('JWT Verify Error:', err);
        return null;
    }
}
