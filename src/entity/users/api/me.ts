'use server';

import {User} from "@/entity/users/model";
import {cookies, headers} from "next/headers";

export type GetMeModel = {
    success: boolean,
    error?: string;
    user?: User
}

export async function getMeMiddleware(token: string, host = ''): Promise<GetMeModel> {
    const response = await fetch(host + '/api/me', {
        headers: {
            authorization: token
        }
    })

    if(!response.ok){
        return {
            success: false,
            error: 'User topilmadi!'
        }
    }

    const {user}: GetMeModel = await response.json()

    if(!user){
        return {
            success: false,
            error: 'User topilmadi!'
        }
    }

    return {
        success: true,
        user
    }
}

export async function getMe () {
    const hdrs = await headers()
    const host = hdrs.get('host')!
    const cks = await cookies()
    return getMeMiddleware(cks.get('token')?.value || '', `http:${host}`)
}