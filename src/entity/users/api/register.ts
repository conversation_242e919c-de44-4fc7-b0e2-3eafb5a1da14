'use server';

import {dbConnect} from "@/shared/lib/database";
import {UserSchema} from "@/entity/users/schema";
import {hashPassword} from "@/entity/users/api/hash";

type AuthResult = {
    success: boolean
    error?: string
}

const tempUser = {
    email: '<EMAIL>',
    password: 'koktol'
}

export async function register(formData: FormData): Promise<AuthResult> {

    const email = formData.get("email") as string
    const password = formData.get("password") as string

    // Basic validation
    if (!email || !email.includes("@")) {
        return {
            success: false,
            error: "Please enter a valid email address",
        }
    }

    if (!password || password.length < 6) {
        return {
            success: false,
            error: "Password must be at least 6 characters long",
        }
    }

    if(tempUser.email !== email || tempUser.password !== password ){
        return {
            success: false,
            error: "Noto'g'ri email va parol",
        }
    }

    await dbConnect()

    const user = await UserSchema.findOne({email})

    if(!user){
        const hashedPass = await hashPassword(password)
        await UserSchema.create({
            email,
            password: hashedPass,
            role: 'SUPER_ADMIN',
        })

        return { success: true }
    }

    return { success: true }
}