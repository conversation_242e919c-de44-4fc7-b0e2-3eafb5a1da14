import { z } from "zod"

/**
 * Схема для формы добавления номера телефона
 */
export const addPhoneSchema = z.object({
  voteId: z.string()
    .min(1, { message: "Ovoz tanlash kerak" }),
  phoneNumber: z.string()
    .min(1, { message: "Telefon raqami kiritilishi shart" })
    .regex(/^\+?[0-9]{7,15}$/, {
      message: "Telefon raqami noto'g'ri formatda. Misol: +998901234567"
    }),
  ownerId: z.string().optional()
})

export type AddPhoneFormValues = z.infer<typeof addPhoneSchema>
