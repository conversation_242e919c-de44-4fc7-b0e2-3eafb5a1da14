import {createPaginationList, PaginationList} from "@/shared/api/external";
import {VoteList} from "@/entity/votes/api";
import {makeAutoObservable} from "mobx";


export class VoteListModel {
    votes: PaginationList<VoteList>

    constructor() {
        this.votes = createPaginationList<VoteList>()

        makeAutoObservable(this, {}, {autoBind: true})
    }

    setVotes(votes: PaginationList<VoteList>) {
        this.votes = votes
    }
}