"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { useDebounce } from "@/shared/lib/use-debounce"
import { toast } from "sonner"
import { VoteList, getVotesPaginated } from "../api"

/**
 * Хук для получения списка голосов для селекта
 */
export function useVotesSelect() {
  const [search, setSearch] = useState("")
  const debouncedSearch = useDebounce(search, 300)
  
  // Запрос данных с использованием react-query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<{ data: VoteList[] }>({
    queryKey: ["votesSelect", debouncedSearch],
    queryFn: async () => {
      try {
        // Получаем голоса без номера телефона и без привязки к хозяйству
        return await getVotesPaginated({
          page: 1,
          size: 100,
          phoneNumber: debouncedSearch,
          excludeWithPhone: true, // Исключаем голоса с номером телефона
          excludeWithOwner: true // Исключаем голоса, привязанные к хозяйству
        })
      } catch (error) {
        console.error("Error fetching votes for select:", error)
        toast.error("Ovozlarni yuklashda xatolik yuz berdi")
        throw error
      }
    }
  })
  
  // Преобразуем голоса в формат для селекта
  const votesForSelect = (data?.data || []).map(vote => ({
    value: vote._id,
    label: `Ovoz ${vote.voteDate || 'без даты'}`
  }))
  
  return {
    votes: votesForSelect,
    isLoading,
    isError,
    error,
    search,
    setSearch,
    refetch
  }
}
