"use client"

import { toast } from "sonner"
import { useQuery } from "@tanstack/react-query"
import { VotesListParams } from "@/entity/votes/api"
import { useState } from "react"
import {getVotes} from "@/shared/api/external";

/**
 * Хук для получения списка голосов
 */
export function useOriginalVotesList(token: string) {
  const [params, setParams] = useState<VotesListParams>({page: 1})

  // Создаем параметры запроса с учетом debounce
  const queryParams = {
    ...params,
  }

  // Запрос данных с использованием react-query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["votes", queryParams, token],
    queryFn: async () => {
      try {
        return await getVotes({token, page: queryParams.page?.toString()})
      } catch (error) {
        console.error("Error fetching votes:", error)
        toast("Xatolik", {
          description: "Ovozlarni yuk<PERSON> xatolik yuz berdi",
        })
        throw error
      }
    },
    enabled: !!token
  })

  return {
    votes: data?.content || [],
    total: data?.totalElements || 0,
    totalPages: data?.totalPage || 0,
    currentPage: data?.number || 1,
    isLoading,
    isError,
    error,
    refetch,
    setParams
  }
}
