import {useEffect, useState} from "react";
import {ExternalVoteCaptcha, getCaptcha, getInitiativeToken} from "@/shared/api/external";
import {toast} from "react-toastify";

export function useVoteToken () {
  const [captcha, setCaptcha] = useState<ExternalVoteCaptcha | undefined>(undefined)
  const [captchaAns, setCaptchaAns] = useState('')
  const [token, setToken] = useState('')

  const refreshCaptcha = async () => {
    const c = await getCaptcha()
    setCaptcha(c)
    setCaptchaAns('')
  }

  const getToken = async () => {
    if(!captcha || !captchaAns){
      return
    }

    try {
      const body = {
        initiativeId: "192db02b-019f-4645-8490-69f6e72902ad",
        captchaKey: captcha.captchaKey,
        captchaResult: captchaAns
      }

      const t = await getInitiativeToken(body)
      setToken(t.token)
    }catch (e) {
      toast.error(e as string, {
        toastId: 'get-token-error'
      })
    }
  }

  const base64 = (b: string) => {
    return `data:image/png;base64, ${b}`
  }

  useEffect(() => {
    void refreshCaptcha()
  }, []);

  return {
    captcha,
    captchaAns,
    setCaptchaAns,
    refreshCaptcha,
    getToken,
    token,
    base64
  }
}