"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { addPhone } from "../api"
import { toast } from "sonner"

/**
 * Хук для добавления номера телефона
 */
export function useAddPhone() {
  const queryClient = useQueryClient()
  
  const mutation = useMutation({
    mutationFn: addPhone,
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Telefon raqami muvaffaqiyatli qo'shildi")
        
        // Инвалидируем кеш для обновления данных
        queryClient.invalidateQueries({ queryKey: ["votes"] })
        queryClient.invalidateQueries({ queryKey: ["villagePersonVotes"] })
        
        return true
      } else {
        toast.error(data.error || "Telefon raqamini qo'shishda xatolik yuz berdi")
        return false
      }
    },
    onError: (error) => {
      console.error("Error adding phone:", error)
      toast.error("Telefon raqamini qo'shishda xatolik yuz berdi")
      return false
    }
  })
  
  return mutation
}
