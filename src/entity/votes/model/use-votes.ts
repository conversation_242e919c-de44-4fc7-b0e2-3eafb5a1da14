"use client"

import { toast } from "sonner"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { VotesListParams, getVotesPaginated, updateVotes } from "@/entity/votes/api"
import { useState } from "react"
import { useDebounce } from "@/shared/lib/use-debounce"

/**
 * Хук для получения списка голосов
 */
export function useVotesList(initialParams: VotesListParams = {}) {
  const [params, setParams] = useState<VotesListParams>(initialParams)

  // Используем debounce для поискового запроса
  const debouncedPhoneNumber = useDebounce(params.phoneNumber || "", 300)

  // Создаем параметры запроса с учетом debounce
  const queryParams = {
    ...params,
    phoneNumber: debouncedPhoneNumber
  }

  // Запрос данных с использованием react-query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["votes", queryParams],
    queryFn: async () => {
      try {
        return await getVotesPaginated(queryParams)
      } catch (error) {
        console.error("Error fetching votes:", error)
        toast("Xatolik", {
          description: "Ovozlarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    }
  })

  return {
    votes: data?.data || [],
    total: data?.total || 0,
    totalPages: data?.totalPages || 0,
    currentPage: data?.currentPage || 1,
    isLoading,
    isError,
    error,
    refetch,
    setParams
  }
}

/**
 * Хук для обновления голосов (привязки к хозяйству)
 */
export function useUpdateVotes() {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: updateVotes,
    onSuccess: (data) => {
      if (data.success) {
        toast("Muvaffaqiyatli", {
          description: `${data.data?.modifiedCount} ta ovoz muvaffaqiyatli saqlandi`,
        })

        // Инвалидируем кеш для обновления данных
        queryClient.invalidateQueries({ queryKey: ["votes"] })

        return true
      } else {
        toast("Xatolik", {
          description: data.error || "Ovozlarni saqlashda xatolik yuz berdi",
        })
        return false
      }
    },
    onError: (error) => {
      console.error("Error updating votes:", error)
      toast("Xatolik", {
        description: "Ovozlarni saqlashda xatolik yuz berdi",
      })
      return false
    }
  })

  return mutation
}

/**
 * Хук для поиска голосов для выбора (без привязки к хозяйству)
 */
export function useVotesForSelect() {
  const [search, setSearch] = useState("")
  const debouncedSearch = useDebounce(search, 300)

  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["votesForSelect", debouncedSearch],
    queryFn: async () => {
      try {
        return await getVotesPaginated({
          page: 1,
          size: 100,
          phoneNumber: debouncedSearch,
          excludeWithOwner: true,
          onlyWithPhone: true
        })
      } catch (error) {
        console.error("Error fetching votes for select:", error)
        toast("Xatolik", {
          description: "Ovozlarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    }
  })

  return {
    votes: data?.data || [],
    isLoading,
    isError,
    error,
    search,
    setSearch,
    refetch
  }
}
