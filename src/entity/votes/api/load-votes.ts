'use server';

import fs from 'fs/promises'
import fsStream from 'fs'
import path from 'path'
import {ExternalVotesModel} from "@/shared/api/external";
import {dbConnect} from "@/shared/lib/database";
import {VotesSchema} from "@/entity/votes/schema";
import {getExternalImageUrl} from "@/shared/lib/external-images";
import {Readable} from "node:stream";

export async function loadVotes (votes: ExternalVotesModel[]) {
    try {

        await downloadAllFiles(votes.map(vote => vote.image))

        await dbConnect()

        const result = await VotesSchema.insertMany(votes.map(vote => ({imageKey: vote.image, voteDate: vote.voteDate})));
        console.log(`${result.length} documents were inserted`, result);

        return true

    }catch (err){
        console.log('Error in votes load ', err)
        return false
    }
}

async function downloadAndSaveFile(url: string, outputDir: string, fileName: string) {
    try {
        const filePath = path.join(outputDir, fileName);

        // Создаем папку, если ее нет
        await fs.mkdir(outputDir, { recursive: true });

        // Загружаем файл с помощью fetch
        const response = await fetch(url);
        if (!response.ok || !response.body) {
            throw new Error(`Не удалось загрузить ${url}: ${response.statusText}`);
        }

        // Преобразуем Web stream в Node.js stream
        const nodeStream = Readable.fromWeb(response.body as any);
        const writer = fsStream.createWriteStream(filePath);

        nodeStream.pipe(writer);

        // Ждем завершения записи
        await new Promise((resolve, reject) => {
            // @ts-expect-error
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        console.log(`Файл ${fileName} успешно загружен и сохранен`);
    } catch (error) {
        console.error(`Ошибка при загрузке ${url}:`, (error as Error)?.message);
    }
}

// Функция для обработки всех ссылок
export async function downloadAllFiles (urls: string[]) {
    const output = path.join(process.cwd(), 'public', 'votePictures')
    const promises = urls.map(url => downloadAndSaveFile(getExternalImageUrl(url), output, `${url}.png`))
    await Promise.all(promises)
}