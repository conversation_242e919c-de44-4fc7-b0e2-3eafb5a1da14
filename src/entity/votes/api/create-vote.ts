'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";
import fs from 'fs/promises';
import path from 'path';
import { Readable } from "node:stream";
import fsStream from 'fs';

export type CreateVoteParams = {
    imageKey: string;
    voteDate: string;
    ownerId?: string; // ID хозяйства, к которому привязан голос (необязательное поле)
    imageFile?: File; // Файл изображения (если загружается с клиента)
};

/**
 * Создает новый голос в базе данных
 */
export async function createVote(params: CreateVoteParams) {
    try {
        await dbConnect();

        const { imageKey, voteDate, ownerId, imageFile } = params;

        // Если передан файл изображения, сохраняем его
        if (imageFile) {
            await saveImageFile(imageFile, imageKey);
        }

        // Создаем документ для сохранения в базе данных
        const voteData: any = {
            imageKey,
            voteDate,
        };

        // Если передан ownerId, добавляем его в документ
        if (ownerId) {
            voteData.ownerId = ownerId;
        }

        // Сохраняем голос в базе данных
        const vote = await VotesSchema.create(voteData);

        return {
            success: true,
            data: {
                _id: vote._id.toString(),
                imageKey: vote.imageKey,
                voteDate: vote.voteDate,
                ownerId: vote.ownerId ? vote.ownerId.toString() : undefined,
                createdAt: vote.createdAt.toISOString(),
                updatedAt: vote.updatedAt.toISOString(),
            }
        };
    } catch (error) {
        console.error('Error creating vote:', error);
        return {
            success: false,
            error: (error as Error).message || 'Failed to create vote'
        };
    }
}

/**
 * Сохраняет файл изображения на сервере
 */
async function saveImageFile(file: File, fileName: string) {
    try {
        const outputDir = path.join(process.cwd(), 'public', 'votePictures');
        const filePath = path.join(outputDir, `${fileName}.png`);

        // Создаем папку, если ее нет
        await fs.mkdir(outputDir, { recursive: true });

        // Получаем содержимое файла
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Сохраняем файл
        await fs.writeFile(filePath, buffer);

        console.log(`Файл ${fileName} успешно сохранен`);
        return true;
    } catch (error) {
        console.error(`Ошибка при сохранении файла ${fileName}:`, (error as Error)?.message);
        throw error;
    }
}
