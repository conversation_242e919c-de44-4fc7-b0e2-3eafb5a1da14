import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import {verifyJwt} from "@/entity/users/api/jwt";
import {getMeMiddleware} from "@/entity/users/api/me";

export const config = {
    matcher: ["/dashboard/:path*", "/"],
};

export async function middleware(req: NextRequest) {
    const token = req.cookies.get("token")?.value;

    const verified = token && (await verifyJwt(token));
    const isProtectedRoute = req.nextUrl.pathname.startsWith("/dashboard"); // защита по пути

    if (isProtectedRoute && !verified) {
        return NextResponse.redirect(new URL("/login", req.url));
    }

    const {user} = await getMeMiddleware(token!, req.nextUrl.origin)

    if(!user && isProtectedRoute){
        return NextResponse.redirect(new URL("/login", req.url));
    }

    if(req.nextUrl.pathname === '/'){
        return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    return NextResponse.next();
}
