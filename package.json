{"name": "voices-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.72.2", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "mongoose": "^8.13.2", "next": "15.2.4", "next-state-adapter": "^0.0.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "query-string": "^9.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-imask": "^7.6.1", "react-toastify": "^11.0.5", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^7.0.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}